# Spider Admin 使用指南

## 🚀 快速开始

### 1. 访问首页
打开浏览器访问 `http://localhost:3000`，您将看到：
- 欢迎区域显示系统统计信息
- 三个服务卡片：用户管理服务、数据分析服务、系统监控服务
- 快速操作区域

### 2. 进入服务
点击任意服务卡片的"进入服务"按钮，进入服务详情页面：
- 查看服务基本信息和状态
- 浏览所有功能模块
- 查看服务统计数据

### 3. 使用功能模块
在服务详情页面，点击功能模块卡片进入具体功能：
- **演示模式**：显示功能演示界面
- **iframe模式**：嵌入外部网站（如配置了外部URL）

## 🎯 数据看板集成

### 访问数据看板
1. 从首页点击"数据分析服务"
2. 在服务详情页面点击"数据看板"功能
3. 系统将自动加载外部数据看板：`http://gripper.aizinger.com:8080/app/static/chat.html?scene=automation_app`

### 数据看板功能
- **全屏显示**：iframe占据整个内容区域
- **刷新功能**：点击"刷新"按钮重新加载页面
- **新窗口打开**：点击"新窗口打开"在新标签页中查看
- **响应式设计**：自适应不同屏幕尺寸

## 🔧 服务配置

### 当前服务配置

#### 用户管理服务 (端口 8081)
- **用户管理**：用户列表、添加、编辑、删除
- **角色管理**：角色分配和权限控制
- **权限管理**：细粒度权限设置
- **部门管理**：组织架构管理
- **操作审计**：用户操作日志

#### 数据分析服务 (端口 8082)
- **数据看板**：🎯 **集成外部网站** - 实时数据展示
- **报表管理**：报表生成和管理
- **图表分析**：数据可视化图表
- **数据导出**：数据导出功能
- **定时任务**：自动化数据处理

#### 系统监控服务 (端口 8083)
- **系统监控**：实时系统状态监控
- **性能分析**：系统性能指标分析
- **日志管理**：系统日志查看和分析
- **告警管理**：系统告警设置和通知
- **健康检查**：服务健康状态检查

## 🎨 界面特性

### 导航系统
- **侧边栏**：可折叠的服务和功能导航
- **面包屑**：显示当前位置路径
- **顶部导航**：环境标识、用户信息

### 响应式设计
- **桌面端**：完整功能展示
- **平板端**：优化的布局适配
- **移动端**：简化的界面设计

### 主题系统
- **浅色主题**：默认主题
- **深色主题**：可通过CSS变量切换
- **自定义颜色**：支持品牌色定制

## 🔄 环境切换

### 开发环境
- 服务地址：`localhost:808x`
- 调试模式：启用开发工具
- 热重载：代码修改自动刷新

### 生产环境
- 服务地址：配置的生产域名
- 优化构建：压缩和优化代码
- 错误处理：生产级错误处理

## 📱 移动端使用

### 触摸操作
- **点击**：选择和导航
- **滑动**：侧边栏展开/收起
- **双击**：快速操作

### 移动端优化
- **简化导航**：折叠式菜单
- **触摸友好**：大按钮和间距
- **性能优化**：减少动画和特效

## 🛠️ 故障排除

### 常见问题

1. **页面无法加载**
   - 检查网络连接
   - 确认服务器状态
   - 清除浏览器缓存

2. **iframe内容不显示**
   - 检查外部网站可访问性
   - 确认浏览器安全设置
   - 查看控制台错误信息

3. **功能按钮无响应**
   - 刷新页面重试
   - 检查JavaScript错误
   - 确认浏览器兼容性

### 浏览器兼容性
- **Chrome**: 90+ ✅
- **Firefox**: 88+ ✅
- **Safari**: 14+ ✅
- **Edge**: 90+ ✅

## 📞 技术支持

### 获取帮助
1. 查看本使用指南
2. 检查 [iframe集成指南](docs/IFRAME_INTEGRATION.md)
3. 查看浏览器控制台错误
4. 联系技术支持团队

### 反馈渠道
- 功能建议
- 问题报告
- 使用体验反馈

---

**Spider Admin** - 让多服务管理变得简单高效 🕷️
