# iframe 集成指南

本文档说明如何在 Spider Admin 中集成外部网站或应用。

## 🎯 功能概述

Spider Admin 支持通过 iframe 嵌入外部网站或应用，特别适用于：
- 第三方数据看板
- 外部管理工具
- 独立的Web应用
- 报表系统

## 🔧 配置方法

### 1. 添加外部URL配置

在 `src/views/ServiceFeature.vue` 文件中，找到 `EXTERNAL_URLS` 配置对象：

```typescript
// 特定功能的外部URL配置
const EXTERNAL_URLS: Record<string, string> = {
  'service2-dashboard': 'http://gripper.aizinger.com:8080/app/static/chat.html?scene=automation_app'
}
```

### 2. 配置规则

配置键的格式为：`{serviceId}-{featureId}`

- `serviceId`: 服务的ID（如 service1, service2, service3）
- `featureId`: 功能的ID（如 dashboard, reports, charts）

### 3. 示例配置

```typescript
const EXTERNAL_URLS: Record<string, string> = {
  // 数据分析服务的数据看板
  'service2-dashboard': 'http://gripper.aizinger.com:8080/app/static/chat.html?scene=automation_app',
  
  // 用户管理服务的报表页面
  'service1-reports': 'https://your-reports.example.com',
  
  // 系统监控服务的监控面板
  'service3-monitor': 'https://your-monitor.example.com/dashboard'
}
```

## 🎨 iframe 功能特性

### 安全特性
- 使用 `sandbox` 属性限制iframe权限
- 支持 `allow-same-origin allow-scripts allow-popups allow-forms`
- 防止恶意脚本执行

### 用户体验
- **刷新按钮**: 重新加载iframe内容
- **新窗口打开**: 在新标签页中打开外部网站
- **加载状态**: 显示页面加载完成提示
- **响应式设计**: 自适应不同屏幕尺寸

### 界面元素
- iframe标题显示当前功能名称
- 操作按钮位于iframe顶部
- 统一的边框和圆角样式

## 📱 响应式支持

iframe容器会根据屏幕尺寸自动调整：

```css
.iframe-container {
  width: 100%;
  height: 700px; /* 桌面端 */
}

@media (max-width: 768px) {
  .iframe-container {
    height: 400px; /* 移动端 */
  }
}
```

## 🔍 故障排除

### 常见问题

1. **iframe无法加载**
   - 检查目标网站是否允许被嵌入（X-Frame-Options）
   - 确认URL是否正确且可访问
   - 检查网络连接

2. **功能按钮不显示**
   - 确认配置键格式正确：`serviceId-featureId`
   - 检查服务和功能ID是否匹配

3. **样式显示异常**
   - 检查CSS样式是否被覆盖
   - 确认iframe容器高度设置

### 调试方法

1. **检查配置**：
```javascript
console.log('当前服务ID:', currentService.value?.id)
console.log('当前功能ID:', currentFeature.value?.id)
console.log('配置键:', `${currentService.value?.id}-${currentFeature.value?.id}`)
```

2. **检查URL**：
```javascript
console.log('iframe URL:', featureUrl.value)
```

## 🛡️ 安全注意事项

1. **HTTPS优先**: 生产环境建议使用HTTPS URL
2. **域名验证**: 确保嵌入的网站来源可信
3. **内容安全策略**: 配置适当的CSP头部
4. **权限控制**: 使用最小权限原则配置sandbox

## 📝 最佳实践

1. **URL管理**: 将外部URL配置集中管理
2. **错误处理**: 添加iframe加载失败的处理逻辑
3. **性能优化**: 考虑懒加载大型iframe内容
4. **用户体验**: 提供加载状态和错误提示

## 🔄 更新配置

修改配置后，开发环境会自动热重载。生产环境需要重新构建和部署：

```bash
npm run build
```

## 📞 技术支持

如果遇到问题，请：
1. 检查浏览器控制台错误信息
2. 确认网络连接和URL可访问性
3. 查看本文档的故障排除部分
4. 联系开发团队获取支持
