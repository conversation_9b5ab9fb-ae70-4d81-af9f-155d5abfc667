# Spider Admin

一个基于 Vue 3 + Element Plus 的多服务统一管理平台。

## 🚀 功能特性

- **多服务管理**: 统一管理多个后端服务，支持不同端口配置
- **iframe集成**: 支持嵌入外部网站和第三方应用
- **环境配置**: 支持开发和生产环境的独立配置
- **响应式设计**: 适配桌面端和移动端
- **模块化架构**: 易于扩展和维护
- **TypeScript**: 完整的类型支持
- **现代化UI**: 基于 Element Plus 的美观界面

## 🛠️ 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **HTTP客户端**: Axios
- **代码规范**: ESLint + Prettier

## 📦 项目结构

```
spider-admin/
├── src/
│   ├── components/          # 公共组件
│   │   ├── demos/          # 演示组件
│   │   ├── SidebarMenu.vue # 侧边栏菜单
│   │   └── TopNavbar.vue   # 顶部导航
│   ├── config/             # 配置文件
│   │   └── services.ts     # 服务配置
│   ├── layouts/            # 布局组件
│   │   └── MainLayout.vue  # 主布局
│   ├── router/             # 路由配置
│   │   └── index.ts        # 路由定义
│   ├── stores/             # 状态管理
│   │   └── app.ts          # 应用状态
│   ├── styles/             # 样式文件
│   │   ├── index.css       # 全局样式
│   │   └── variables.css   # CSS变量
│   ├── utils/              # 工具函数
│   │   └── api.ts          # API封装
│   ├── views/              # 页面组件
│   │   ├── Home.vue        # 首页
│   │   ├── ServiceDetail.vue # 服务详情
│   │   ├── ServiceFeature.vue # 服务功能
│   │   └── NotFound.vue    # 404页面
│   ├── App.vue             # 根组件
│   └── main.ts             # 入口文件
├── scripts/                # 脚本文件
│   └── deploy.js           # 部署脚本
├── .env                    # 环境变量
├── .env.development        # 开发环境变量
├── .env.production         # 生产环境变量
├── deploy.sh               # Linux部署脚本
├── deploy.bat              # Windows部署脚本
├── vite.config.ts          # Vite配置
├── tsconfig.json           # TypeScript配置
└── package.json            # 项目配置
```

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖

```bash
npm install
```

### 开发环境

```bash
npm run dev
```

访问 http://localhost:3000

### 构建项目

```bash
# 生产环境构建
npm run build

# 开发环境构建
npm run build:dev
```

### 预览构建结果

```bash
npm run preview
```

## 🔧 配置说明

### 服务配置

在 `src/config/services.ts` 中配置您的服务信息：

```typescript
export const SERVICES: ServiceConfig[] = [
  {
    id: 'service1',
    name: '用户管理服务',
    description: '用户账户管理、权限控制、角色分配',
    url: 'http://localhost:8081',
    port: 8081,
    icon: 'User',
    color: '#409EFF',
    features: [
      { id: 'users', name: '用户管理', path: '/users', icon: 'UserFilled' },
      // ... 更多功能
    ]
  },
  // ... 更多服务
]
```

### 环境变量

创建 `.env.local` 文件来覆盖默认配置：

```bash
# 应用标题
VITE_APP_TITLE=My Spider Admin

# 服务地址
VITE_SERVICE1_URL=http://your-service1.com:8081
VITE_SERVICE2_URL=http://your-service2.com:8082
VITE_SERVICE3_URL=http://your-service3.com:8083
```

## 📦 部署

### 自动部署

使用提供的部署脚本：

```bash
# Linux/macOS
./deploy.sh production

# Windows
deploy.bat production
```

### 手动部署

1. 构建项目：
```bash
npm run build
```

2. 将 `dist` 目录内容部署到您的Web服务器

3. 配置Web服务器支持SPA路由

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/spider-admin;
    index index.html;

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 🔌 添加新服务

1. 在 `src/config/services.ts` 中添加服务配置
2. 在 `src/components/demos/` 中创建对应的演示组件
3. 更新环境变量文件添加服务URL配置

## 🖼️ iframe集成

支持嵌入外部网站或第三方应用。在 `src/views/ServiceFeature.vue` 中配置：

```typescript
const EXTERNAL_URLS: Record<string, string> = {
  'service2-dashboard': 'http://gripper.aizinger.com:8080/app/static/chat.html?scene=automation_app'
}
```

详细配置说明请参考 [iframe集成指南](docs/IFRAME_INTEGRATION.md)。

## 🎨 自定义主题

在 `src/styles/variables.css` 中修改CSS变量来自定义主题：

```css
:root {
  --primary-color: #your-color;
  --service1-color: #your-service1-color;
  /* ... 更多变量 */
}
```

## 🧪 测试

```bash
# 运行测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage
```

## 📝 代码规范

```bash
# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 支持

如果您遇到问题或有建议，请：

1. 查看 [FAQ](docs/FAQ.md)
2. 搜索现有的 [Issues](https://github.com/your-repo/spider-admin/issues)
3. 创建新的 Issue

## 🔗 相关链接

- [Vue 3 文档](https://vuejs.org/)
- [Element Plus 文档](https://element-plus.org/)
- [Vite 文档](https://vitejs.dev/)
- [TypeScript 文档](https://www.typescriptlang.org/)

---

**Spider Admin** - 让多服务管理变得简单高效 🕷️
